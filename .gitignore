# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
*.local
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
.yarn/install-state.gz

# log
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
dist
dist-ssr

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# storybook
storybook-static

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json
.history/*

# Editor directories and files
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

.claude
CLAUDE.md
