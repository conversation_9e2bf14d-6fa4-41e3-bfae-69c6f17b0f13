#!/usr/bin/env node

const https = require('https');
const chalk = require('chalk');
const inquirer = require('inquirer');
inquirer.registerPrompt(
  'checkbox-plus',
  require('inquirer-checkbox-plus-prompt'),
);

const projectID = 7;
const host = 'platform-api.onesoil.ai';
const token =
  'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************.G6VrufQ2J1CrWieQWKGbSKlqRax0nh3sySPmjkaOSP4';
const langs = [
  'cs',
  'en',
  'es',
  'pt',
  'ru',
  'uk',
  'de',
  'fr',
  'hu',
  'pl',
  'it',
  'bg',
  'ro',
  'tr',
  'el',
];

const translations = {};
const keys = [];
let toSaveKeys = [];
let issue = '';

for (const lang of langs) {
  try {
    translations[lang] = require(`../src/assets/i18n/${lang}.json`);
  } catch {
    translations[lang] = {};
  }

  for (const key in translations[lang]) {
    !keys.includes(key) && keys.push(key);
  }
}

function requestKeys() {
  return new Promise((resolve, reject) => {
    https
      .request(
        {
          host,
          path: `/ru/v2/translations-keys?filter{project_id.in}=2&filter{project_id.in}=${projectID}`,
          headers: {
            Authorization: 'Token ' + token,
          },
        },
        (res) => {
          let str = '';

          res.on('data', function (chunk) {
            str += chunk;
          });

          res.on('end', async function () {
            const data = JSON.parse(str).data;
            resolve(data);
          });

          res.on('error', (error) => {
            console.log(`${error}`);
          });
        },
      )
      .end();
  });
}

async function checkNewKeys() {
  let serverKeys = await requestKeys();
  serverKeys = serverKeys.map((keyData) => keyData.key);
  keys.forEach((key) => !serverKeys.includes(key) && toSaveKeys.push(key));
}

async function askIssue() {
  const answer = await inquirer.prompt([
    {
      name: 'issue',
      type: 'input',
      message: 'Please enter localization issue number (e.g OP-123):',
    },
  ]);

  issue = answer.issue.toUpperCase();
}

async function askAction() {
  let newTranslations = {};

  for (const lang of langs) {
    for (const key of toSaveKeys) {
      if (translations[lang][key]) {
        newTranslations[lang] = newTranslations[lang] || {};
        newTranslations[lang][key] = translations[lang][key];
      }
    }
  }

  console.log(
    `${chalk.redBright('Issue:')} ${chalk.green(issue)} ${chalk.grey(
      `(https://youtrack.onesoil.ai/issue/${issue})`,
    )}\n`,
  );

  for (const lang in newTranslations) {
    console.log(chalk.bgYellow.bold.black(` ${lang} `));
    for (let key in newTranslations[lang]) {
      console.log(
        `    ${chalk.green(key)}: ${chalk.grey(
          JSON.stringify(newTranslations[lang][key]),
        )}`,
      );
    }
    console.log('\n');
  }

  const { todo } = await inquirer.prompt([
    {
      name: 'todo',
      type: 'list',
      message: 'What to do?',
      choices: [
        ...(toSaveKeys.length ? [] : ['Check new keys']),
        ...['Save to server', 'Add keys', 'Remove keys', 'Cancel'],
      ],
    },
  ]);

  return todo;
}

async function actionSave() {
  let serverKeys = await requestKeys();
  const serverKeysName = serverKeys.map((keyData) => keyData.key);
  const newKeys = toSaveKeys.filter((key) => !serverKeysName.includes(keys));

  for (const key of toSaveKeys) {
    const serverKey = serverKeys.find((keyData) => keyData.key === key);
    /*eslint no-loop-func: "off"*/
    await new Promise((resolve, reject) => {
      const req = https.request(
        {
          host,
          path: `/ru/v2/translations-keys${
            serverKey ? '/' + serverKey.id : ''
          }`,
          method: serverKey ? 'PATCH' : 'POST',
          headers: {
            Authorization: 'Token ' + token,
            'Content-Type': 'application/json',
          },
        },
        (res) => {
          res.on('data', (chunk) => {
            //console.log(`${chunk}`);
          });
          res.on('end', function () {
            resolve();
          });
          res.on('error', (error) => {
            console.log(`${error}`);
          });
        },
      );
      req.write(
        JSON.stringify(
          serverKey ? { issue } : { project_id: projectID, key, issue },
        ),
      );
      req.end();
    });
  }

  serverKeys = await requestKeys();

  for (const key of newKeys) {
    const serverKey = serverKeys.find((keyData) => keyData.key === key);
    const affectedLangs = langs.filter((lang) => translations[lang][key]);

    for (const lang of affectedLangs) {
      await new Promise((resolve, reject) => {
        const req = https.request(
          {
            host,
            path: `/${lang}/v2/translations`,
            method: 'POST',
            headers: {
              Authorization: 'Token ' + token,
              'Content-Type': 'application/json',
            },
          },
          (res) => {
            res.on('data', (chunk) => {
              //console.log(`${chunk}`);
            });
            res.on('end', function () {
              resolve();
            });
            res.on('error', (error) => {
              console.log(`${error}`);
            });
          },
        );
        req.write(
          JSON.stringify({
            key_id: serverKey.id,
            language: lang,
            translation: translations[lang][key],
          }),
        );
        req.end();
      });
    }
  }

  process.exit();
}

async function actionAdd() {
  const { addKeys } = await inquirer.prompt([
    {
      name: 'addKeys',
      type: 'checkbox-plus',
      searchable: true,
      message: 'What key do you want to add?',
      source: (answersSoFar, input) => {
        return Promise.resolve(
          (input ? keys.filter((key) => key.startsWith(input)) : keys).filter(
            (key) => !toSaveKeys.includes(key),
          ),
        );
      },
    },
  ]);

  for (const key of addKeys) {
    !toSaveKeys.includes(key) && toSaveKeys.push(key);
  }
}

async function actionRemove() {
  const { skipKeys } = await inquirer.prompt([
    {
      name: 'skipKeys',
      type: 'checkbox-plus',
      searchable: true,
      message: 'What key do you want to remove?',
      source: (answersSoFar, input) => {
        return Promise.resolve(
          input
            ? toSaveKeys.filter((key) => key.startsWith(input))
            : toSaveKeys,
        );
      },
    },
  ]);

  toSaveKeys = toSaveKeys.filter((key) => !skipKeys.includes(key));
}

async function main() {
  await askIssue();
  while (true) {
    switch (await askAction()) {
      case 'Check new keys':
        await checkNewKeys();
        break;
      case 'Add keys':
        await actionAdd();
        break;
      case 'Remove keys':
        await actionRemove();
        break;
      case 'Save to server':
        await actionSave();
        break;
      case 'Cancel':
        process.exit();
        break;

      default:
        continue;
    }
  }
}

main();
